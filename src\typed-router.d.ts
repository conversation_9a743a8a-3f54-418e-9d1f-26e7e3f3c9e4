/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/[...404]': RouteRecordInfo<'/[...404]', '/:404(.*)', { 404: ParamValue<true> }, { 404: ParamValue<false> }>,
    '/chart-manage/chart-instance': RouteRecordInfo<'/chart-manage/chart-instance', '/chart-manage/chart-instance', Record<never, never>, Record<never, never>>,
    '/chart-manage/chart-manage': RouteRecordInfo<'/chart-manage/chart-manage', '/chart-manage/chart-manage', Record<never, never>, Record<never, never>>,
    '/chart-manage/dataset': RouteRecordInfo<'/chart-manage/dataset', '/chart-manage/dataset', Record<never, never>, Record<never, never>>,
    '/chart-manage/field-mapping-test': RouteRecordInfo<'/chart-manage/field-mapping-test', '/chart-manage/field-mapping-test', Record<never, never>, Record<never, never>>,
    '/chart-manage/template': RouteRecordInfo<'/chart-manage/template', '/chart-manage/template', Record<never, never>, Record<never, never>>,
    '/contact-address-book/': RouteRecordInfo<'/contact-address-book/', '/contact-address-book', Record<never, never>, Record<never, never>>,
    '/data-hot-tags-manage/': RouteRecordInfo<'/data-hot-tags-manage/', '/data-hot-tags-manage', Record<never, never>, Record<never, never>>,
    '/data-manage/': RouteRecordInfo<'/data-manage/', '/data-manage', Record<never, never>, Record<never, never>>,
    '/dept-client/': RouteRecordInfo<'/dept-client/', '/dept-client', Record<never, never>, Record<never, never>>,
    '/dept-client/ai': RouteRecordInfo<'/dept-client/ai', '/dept-client/ai', Record<never, never>, Record<never, never>>,
    '/dept-client/asean-portrait': RouteRecordInfo<'/dept-client/asean-portrait', '/dept-client/asean-portrait', Record<never, never>, Record<never, never>>,
    '/dept-client/collect': RouteRecordInfo<'/dept-client/collect', '/dept-client/collect', Record<never, never>, Record<never, never>>,
    '/dept-client/creation-workshop': RouteRecordInfo<'/dept-client/creation-workshop', '/dept-client/creation-workshop', Record<never, never>, Record<never, never>>,
    '/dept-client/detail': RouteRecordInfo<'/dept-client/detail', '/dept-client/detail', Record<never, never>, Record<never, never>>,
    '/dept-client/digital-economy': RouteRecordInfo<'/dept-client/digital-economy', '/dept-client/digital-economy', Record<never, never>, Record<never, never>>,
    '/dept-client/event-database': RouteRecordInfo<'/dept-client/event-database', '/dept-client/event-database', Record<never, never>, Record<never, never>>,
    '/dept-client/event-database-me': RouteRecordInfo<'/dept-client/event-database-me', '/dept-client/event-database-me', Record<never, never>, Record<never, never>>,
    '/dept-client/google-event-news': RouteRecordInfo<'/dept-client/google-event-news', '/dept-client/google-event-news', Record<never, never>, Record<never, never>>,
    '/dept-client/i-spreading-knowledge-about-asean': RouteRecordInfo<'/dept-client/i-spreading-knowledge-about-asean', '/dept-client/i-spreading-knowledge-about-asean', Record<never, never>, Record<never, never>>,
    '/dept-client/key-information': RouteRecordInfo<'/dept-client/key-information', '/dept-client/key-information', Record<never, never>, Record<never, never>>,
    '/dept-client/main-layout': RouteRecordInfo<'/dept-client/main-layout', '/dept-client/main-layout', Record<never, never>, Record<never, never>>,
    '/dept-client/news': RouteRecordInfo<'/dept-client/news', '/dept-client/news', Record<never, never>, Record<never, never>>,
    '/dept-client/news-related-me': RouteRecordInfo<'/dept-client/news-related-me', '/dept-client/news-related-me', Record<never, never>, Record<never, never>>,
    '/dept-client/overseas': RouteRecordInfo<'/dept-client/overseas', '/dept-client/overseas', Record<never, never>, Record<never, never>>,
    '/dept-client/overview': RouteRecordInfo<'/dept-client/overview', '/dept-client/overview', Record<never, never>, Record<never, never>>,
    '/dept-client/panoramic-indicator': RouteRecordInfo<'/dept-client/panoramic-indicator', '/dept-client/panoramic-indicator', Record<never, never>, Record<never, never>>,
    '/dept-client/policy-news': RouteRecordInfo<'/dept-client/policy-news', '/dept-client/policy-news', Record<never, never>, Record<never, never>>,
    '/dept-client/policy-news-detail': RouteRecordInfo<'/dept-client/policy-news-detail', '/dept-client/policy-news-detail', Record<never, never>, Record<never, never>>,
    '/dept-client/precise-portrait/': RouteRecordInfo<'/dept-client/precise-portrait/', '/dept-client/precise-portrait', Record<never, never>, Record<never, never>>,
    '/dept-client/risk': RouteRecordInfo<'/dept-client/risk', '/dept-client/risk', Record<never, never>, Record<never, never>>,
    '/dept-client/search-list': RouteRecordInfo<'/dept-client/search-list', '/dept-client/search-list', Record<never, never>, Record<never, never>>,
    '/dept-client/special-subject-me': RouteRecordInfo<'/dept-client/special-subject-me', '/dept-client/special-subject-me', Record<never, never>, Record<never, never>>,
    '/feedback-manage/': RouteRecordInfo<'/feedback-manage/', '/feedback-manage', Record<never, never>, Record<never, never>>,
    '/home': RouteRecordInfo<'/home', '/home', Record<never, never>, Record<never, never>>,
    '/login/': RouteRecordInfo<'/login/', '/login', Record<never, never>, Record<never, never>>,
    '/login/admin': RouteRecordInfo<'/login/admin', '/login/admin', Record<never, never>, Record<never, never>>,
    '/login/login': RouteRecordInfo<'/login/login', '/login/login', Record<never, never>, Record<never, never>>,
    '/logoutReload': RouteRecordInfo<'/logoutReload', '/logoutReload', Record<never, never>, Record<never, never>>,
    '/system-manage/log/': RouteRecordInfo<'/system-manage/log/', '/system-manage/log', Record<never, never>, Record<never, never>>,
    '/system-manage/manage': RouteRecordInfo<'/system-manage/manage', '/system-manage/manage', Record<never, never>, Record<never, never>>,
    '/system-manage/role/': RouteRecordInfo<'/system-manage/role/', '/system-manage/role', Record<never, never>, Record<never, never>>,
    '/system-manage/standard/': RouteRecordInfo<'/system-manage/standard/', '/system-manage/standard', Record<never, never>, Record<never, never>>,
    '/system-manage/user/': RouteRecordInfo<'/system-manage/user/', '/system-manage/user', Record<never, never>, Record<never, never>>,
    '/user-center/': RouteRecordInfo<'/user-center/', '/user-center', Record<never, never>, Record<never, never>>,
    '/user-center/user-center': RouteRecordInfo<'/user-center/user-center', '/user-center/user-center', Record<never, never>, Record<never, never>>,
  }
}
